<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="dd9e5ddd-bc7d-4828-93e0-b5c629725bfa" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/libraries/Maven__com_h2database_h2_1_4_200.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/MarsCodeWorkspaceAppSettings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/MarsCodeWorkspaceAppSettings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-core/src/main/java/com/gl/framework/exception/handler/GlobalExceptionHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-core/src/main/java/com/gl/framework/exception/handler/GlobalExceptionHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-core/src/main/java/com/gl/framework/web/response/Result.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-core/src/main/java/com/gl/framework/web/response/Result.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/py-manager.iml" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/py-manager.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/run-voice-packet-tests.bat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/main/java/com/gl/service/message/service/BaseServiceMessageService.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/main/java/com/gl/service/message/service/BaseServiceMessageService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/main/java/com/gl/service/opus/entity/FollowAnchor.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/main/java/com/gl/service/opus/entity/FollowAnchor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/main/java/com/gl/service/shop/service/ShopService.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/main/java/com/gl/service/shop/service/ShopService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/basis/controller/BasisControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/basis/controller/BasisControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/basis/service/BasisServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/basis/service/BasisServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/broadcastPlan/controller/BroadcastPlanControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/broadcastPlan/controller/BroadcastPlanControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/broadcastPlan/service/BroadcastPlanServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/broadcastPlan/service/BroadcastPlanServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/commercial/controller/CommercialControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/commercial/controller/CommercialControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/device/controller/DeviceControllerSimpleTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/device/controller/DeviceControllerSimpleTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/device/controller/DeviceControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/device/controller/DeviceControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/device/service/DeviceServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/device/service/DeviceServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/installationPackage/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/installationPackage/controller/InstallationPackageControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/installationPackage/controller/InstallationPackageControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/installationPackage/controller/InstallationPackageLogControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/installationPackage/controller/InstallationPackageLogControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/installationPackage/service/InstallationPackageServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/installationPackage/service/InstallationPackageServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/message/controller/BaseServiceMessageControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/message/controller/BaseServiceMessageControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/message/service/BaseServiceMessageServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/message/service/BaseServiceMessageServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/music/controller/BackgroundMusicControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/music/controller/BackgroundMusicControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/music/service/BackgroundMusicServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/music/service/BackgroundMusicServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/opus/controller/WorkControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/opus/controller/WorkControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/opus/service/WorkServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/opus/service/WorkServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/packet/controller/VoicePacketControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/packet/controller/VoicePacketControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/packet/service/VoicePacketServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/packet/service/VoicePacketServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/controller/ShopControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/controller/ShopControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/controller/WeChatControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/controller/WeChatControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/service/ShopServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/service/ShopServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/service/WeChatServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/shop/service/WeChatServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/template/controller/TemplateControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/template/controller/TemplateControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/user/controller/WechatUserControllerTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/user/controller/WechatUserControllerTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/utils/PerformanceTestDataGenerator.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/service/utils/PerformanceTestDataGenerator.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/system/service/PasswodTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin-manager/src/test/java/com/gl/system/service/PasswodTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/resources/application-performance.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/admin-manager/src/test/resources/application-test.yml" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="useMavenConfig" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2ycYWFV5W2IDgsIvND8h6PdPB10" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/yny/4g/py-service&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.31099194&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;ab22ac0b2347079dfa4d10bbd326473c&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\yny\4g\admin\admin-manager\src\main\java\com\gl\service\order\entity" />
      <recent name="D:\yny\4g\admin" />
      <recent name="D:\yny\4g\admin\admin-manager\src\main\java\com\gl\service\opus" />
      <recent name="D:\yny\4g\admin\admin-manager\src\main\java\com\gl\service\shop\repository" />
      <recent name="D:\yny\4g\admin\admin-manager\src\main\java\com\gl\service\opus\entity" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.gl.service.opus.vo" />
      <recent name="com.gl.service.opus.entity" />
      <recent name="com.gl.service.opus.repository" />
      <recent name="com.gl.commons.enums" />
      <recent name="com.gl.service.opus.vo.dto" />
    </key>
  </component>
  <component name="RunManager" selected="JUnit.TemplateControllerTest">
    <configuration name="ShopControllerTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="py-manager" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gl.service.shop.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.gl.service.shop.controller" />
      <option name="MAIN_CLASS_NAME" value="com.gl.service.shop.controller.ShopControllerTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ShopServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="py-manager" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gl.service.shop.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.gl.service.shop.service" />
      <option name="MAIN_CLASS_NAME" value="com.gl.service.shop.service.ShopServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TemplateControllerTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="py-manager" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gl.service.template.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.gl.service.template.controller" />
      <option name="MAIN_CLASS_NAME" value="com.gl.service.template.controller.TemplateControllerTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WeChatControllerTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="py-manager" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gl.service.shop.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.gl.service.shop.controller" />
      <option name="MAIN_CLASS_NAME" value="com.gl.service.shop.controller.WeChatControllerTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WeChatServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="py-manager" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gl.service.shop.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.gl.service.shop.service" />
      <option name="MAIN_CLASS_NAME" value="com.gl.service.shop.service.WeChatServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.TemplateControllerTest" />
        <item itemvalue="JUnit.WeChatServiceTest" />
        <item itemvalue="JUnit.ShopServiceTest" />
        <item itemvalue="JUnit.WeChatControllerTest" />
        <item itemvalue="JUnit.ShopControllerTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="dd9e5ddd-bc7d-4828-93e0-b5c629725bfa" name="Changes" comment="" />
      <created>1750135358478</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750135358478</updated>
      <workItem from="1750135359744" duration="1065000" />
      <workItem from="1750294226264" duration="6001000" />
      <workItem from="1750409377228" duration="410000" />
      <workItem from="1750465763314" duration="374000" />
      <workItem from="1750467690102" duration="1390000" />
      <workItem from="1750482355322" duration="5167000" />
      <workItem from="1750644381769" duration="666000" />
      <workItem from="1750659617532" duration="6041000" />
      <workItem from="1750725191808" duration="5341000" />
      <workItem from="1750732691687" duration="11000" />
      <workItem from="1750742300047" duration="10307000" />
      <workItem from="1750813036684" duration="17966000" />
      <workItem from="1750901635329" duration="10644000" />
      <workItem from="1750986323920" duration="848000" />
      <workItem from="1750987482647" duration="8567000" />
      <workItem from="1751000411915" duration="2932000" />
      <workItem from="1751069842635" duration="2441000" />
      <workItem from="1751097707514" duration="2148000" />
      <workItem from="1751246716825" duration="4437000" />
      <workItem from="1751253910875" duration="11622000" />
      <workItem from="1751329530167" duration="14985000" />
      <workItem from="1751359773384" duration="562000" />
      <workItem from="1751418447524" duration="7030000" />
      <workItem from="1751501858894" duration="4829000" />
      <workItem from="1751598800762" duration="8556000" />
      <workItem from="1751675281967" duration="12404000" />
      <workItem from="1751851881075" duration="2559000" />
      <workItem from="1751869510586" duration="594000" />
      <workItem from="1751940147106" duration="11114000" />
      <workItem from="1752028857983" duration="8170000" />
      <workItem from="1752106943733" duration="4887000" />
      <workItem from="1752220815125" duration="1253000" />
      <workItem from="1752279889204" duration="13022000" />
      <workItem from="1752456640842" duration="20014000" />
      <workItem from="1752539801959" duration="20792000" />
      <workItem from="1752625707856" duration="2749000" />
      <workItem from="1752711562477" duration="597000" />
      <workItem from="1753084198597" duration="154000" />
      <workItem from="1753172010306" duration="3129000" />
      <workItem from="1753248485663" duration="5373000" />
      <workItem from="1753325471837" duration="11000" />
      <workItem from="1753427532934" duration="3226000" />
      <workItem from="1753666699853" duration="1911000" />
      <workItem from="1753750537952" duration="2364000" />
      <workItem from="1753755842991" duration="7265000" />
      <workItem from="1753766674715" duration="3810000" />
      <workItem from="1753839800025" duration="20028000" />
      <workItem from="1753921296726" duration="978000" />
      <workItem from="1753931486416" duration="476000" />
      <workItem from="1753940262956" duration="610000" />
      <workItem from="1753944382500" duration="4793000" />
      <workItem from="1754010532539" duration="13972000" />
      <workItem from="1754093666623" duration="9877000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>