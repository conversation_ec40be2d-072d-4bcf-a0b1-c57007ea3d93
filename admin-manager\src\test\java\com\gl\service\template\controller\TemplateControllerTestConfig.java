package com.gl.service.template.controller;

import com.gl.framework.common.util.redis.RedisUtils;
import com.gl.framework.interceptor.impl.SameUrlDataInterceptor;
import com.gl.framework.security.filter.JwtAuthenticationTokenFilter;
import com.gl.framework.security.handle.AuthenticationEntryPointImpl;
import com.gl.framework.security.handle.LogoutSuccessHandlerImpl;
import com.gl.framework.security.service.TokenService;
import com.gl.framework.security.service.UserDetailsServiceImpl;
import com.gl.framework.security.service.PermissionService;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * TemplateController测试配置类
 * 为单元测试提供简化的安全配置和Mock Bean
 *
 * <AUTHOR>
 */
@TestConfiguration
@EnableWebSecurity
@Order(99)
public class TemplateControllerTestConfig extends WebSecurityConfigurerAdapter {

        @MockBean
        private RedisUtils redisUtils;

        @MockBean
        private SameUrlDataInterceptor sameUrlDataInterceptor;

        @MockBean
        private JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;

        @MockBean
        private AuthenticationEntryPointImpl authenticationEntryPoint;

        @MockBean
        private LogoutSuccessHandlerImpl logoutSuccessHandler;

        @MockBean
        private TokenService tokenService;

        @MockBean
        private UserDetailsServiceImpl userDetailsServiceImpl;

        @MockBean
        private PermissionService permissionService;

        @Override
        protected void configure(HttpSecurity http) throws Exception {
                http
                                .csrf().disable()
                                .authorizeRequests()
                                .anyRequest().permitAll()
                                .and()
                                .httpBasic();
        }

        @Bean
        @Override
        public UserDetailsService userDetailsService() {
                UserDetails user = User.builder()
                                .username("testuser")
                                .password(passwordEncoder().encode("testpass"))
                                .authorities("template:template:list", "template:template:export",
                                                "template:template:addorupdate", "template:template:delete")
                                .build();

                UserDetails adminUser = User.builder()
                                .username("admin")
                                .password(passwordEncoder().encode("admin"))
                                .authorities("template:template:list", "template:template:export",
                                                "template:template:addorupdate", "template:template:delete", "ADMIN")
                                .build();

                UserDetails limitedUser = User.builder()
                                .username("limited")
                                .password(passwordEncoder().encode("limited"))
                                .authorities("other:permission")
                                .build();

                return new InMemoryUserDetailsManager(user, adminUser, limitedUser);
        }

        @Bean("userDetailsServiceImpl")
        public UserDetailsService userDetailsServiceImpl() {
                return userDetailsService();
        }

        @Bean
        public PasswordEncoder passwordEncoder() {
                return new BCryptPasswordEncoder();
        }

        @Bean("ps")
        public PermissionService ps() {
                // 配置Mock行为
                when(permissionService.hasPermi("template:template:list")).thenReturn(true);
                when(permissionService.hasPermi("template:template:export")).thenReturn(true);
                when(permissionService.hasPermi("template:template:addorupdate")).thenReturn(true);
                when(permissionService.hasPermi("template:template:delete")).thenReturn(true);
                when(permissionService.hasPermi(anyString())).thenReturn(false); // 默认返回false
                return permissionService;
        }
}
